"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Edit, Star } from "lucide-react"
import TokenTable from "@/components/token-table"
import EditProfileModal from "@/components/edit-profile-modal"

// Mock data
const userProfile = {
  name: "<PERSON>lal <PERSON>",
  rating: 4.5,
  ordersCompleted: 25,
  image: "/placeholder.svg?height=100&width=100",
}

const userTrades = [
  {
    id: "1",
    name: "Greed3",
    symbol: "Greed3",
    marketCap: "$71,867",
    created: "02/20/2023",
    bonded: "02/20/2023",
    fiveMin: "-0.31",
    oneHour: "-1.78",
    sixHour: "-0.31",
    twentyFourHour: "0.3",
    sevenDay: "39.27",
    chart: "up",
  },
  {
    id: "2",
    name: "<PERSON><PERSON><PERSON>",
    symbol: "Greed3",
    marketCap: "$71,867",
    created: "02/20/2023",
    bonded: "02/20/2023",
    fiveMin: "-0.31",
    oneHour: "-1.78",
    sixHour: "-0.31",
    twentyFourHour: "0.3",
    sevenDay: "39.27",
    chart: "down",
  },
  {
    id: "3",
    name: "Greed3",
    symbol: "Greed3",
    marketCap: "$71,867",
    created: "02/20/2023",
    bonded: "02/20/2023",
    fiveMin: "-0.31",
    oneHour: "-1.78",
    sixHour: "-0.31",
    twentyFourHour: "0.3",
    sevenDay: "39.27",
    chart: "down",
  },
]

const subscriptionPlans = [
  {
    id: "free",
    name: "Free Plan",
    description: "For Individual Users",
    features: [
      "10 free requests per month",
      "Limited access to the AI writing assistant",
      "Limited to basic tools (e.g., Professional and Casual)",
    ],
    isPopular: false,
  },
  {
    id: "enterprise",
    name: "Enterprise",
    description: "For Individual Users",
    features: [
      "10 free requests per month",
      "Limited access to the AI writing assistant",
      "Limited to basic tools (e.g., Professional and Casual)",
    ],
    isPopular: false,
  },
  {
    id: "member",
    name: "Member Plan",
    description: "For Individual Users",
    features: [
      "Access to tools at any time for a monthly fee",
      "Ability to be featured",
      "Receive ranked tokens to make investment decision",
      "AI ranked projects and tools for you",
    ],
    isPopular: true,
  },
]

export default function ProfilePage() {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState("free")

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-start">
            <h2 className="text-xl font-medium">Profile Setting</h2>
            <Button size="sm" className="bg-purple-700 hover:bg-purple-800" onClick={() => setIsEditModalOpen(true)}>
              <Edit size={16} className="mr-1" />
              Edit
            </Button>
          </div>

          <div className="flex items-center mt-4">
            <div className="relative">
              <div className="w-24 h-24 rounded-full border-4 border-yellow-400 overflow-hidden">
                <Image
                  src={userProfile.image || "/placeholder.svg"}
                  alt={userProfile.name}
                  width={100}
                  height={100}
                  className="object-cover"
                />
              </div>
            </div>

            <div className="ml-6">
              <h3 className="text-lg font-medium">{userProfile.name}</h3>
              <div className="flex items-center mt-1">
                <Star className="w-4 h-4 text-yellow-400 fill-yellow-400" />
                <span className="ml-1 text-sm">
                  {userProfile.rating} ({userProfile.ordersCompleted} Orders Completed)
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div>
        <h2 className="text-xl font-medium mb-4">My Trades</h2>
        <Card>
          <CardContent className="p-0">
            <TokenTable tokens={userTrades} />
          </CardContent>
        </Card>
      </div>

      <div>
        <h2 className="text-xl font-medium mb-4">Update Subscription</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {subscriptionPlans.map((plan) => (
            <Card
              key={plan.id}
              className={`${plan.isPopular ? "border-purple-700" : "border-gray-200"} ${
                selectedPlan === plan.id ? "ring-2 ring-purple-700" : ""
              } relative`}
            >
              {plan.isPopular && (
                <div className="absolute top-0 right-0 bg-purple-700 text-white px-3 py-1 text-xs font-bold rounded-bl-lg">
                  POPULAR
                </div>
              )}
              <CardContent className="p-6">
                <h3 className="text-lg font-medium">{plan.name}</h3>
                <p className="text-sm text-gray-500 mb-4">{plan.description}</p>

                <ul className="space-y-2 mb-6">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <div className="w-5 h-5 rounded-full bg-purple-100 flex items-center justify-center mt-0.5 mr-2 flex-shrink-0">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M10 3L4.5 8.5L2 6"
                            stroke="#6D28D9"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                      </div>
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  className={`w-full ${
                    plan.isPopular ? "bg-purple-700 hover:bg-purple-800" : "bg-purple-700 hover:bg-purple-800"
                  }`}
                  onClick={() => setSelectedPlan(plan.id)}
                >
                  Get Started Now
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <EditProfileModal isOpen={isEditModalOpen} onClose={() => setIsEditModalOpen(false)} profile={userProfile} />
    </div>
  )
}
