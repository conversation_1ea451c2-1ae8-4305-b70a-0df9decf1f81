"use client";

import { useAuth } from "@/lib/auth-context";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Loader2 } from "lucide-react";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireSubscription?: boolean;
}

export function ProtectedRoute({ children, requireSubscription = false }: ProtectedRouteProps) {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        // User is not authenticated, redirect to login
        router.push('/login');
        return;
      }

      if (requireSubscription && !user.isSubscribed) {
        // User is not subscribed, redirect to subscription page
        router.push('/subscribe');
        return;
      }
    }
  }, [user, isLoading, router, requireSubscription]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // If user is not authenticated, don't render children
  if (!user) {
    return null;
  }

  // If subscription is required but user is not subscribed, don't render children
  if (requireSubscription && !user.isSubscribed) {
    return null;
  }

  // User is authenticated (and subscribed if required), render children
  return <>{children}</>;
}
