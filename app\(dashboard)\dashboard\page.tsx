"use client"
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"

// Mock data
const topTokens = [
  {
    id: "1",
    name: "Top Token 01",
    symbol: "TT1",
    price: 0.00052153,
    change: 12.5,
    image: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "2",
    name: "Top Token 01",
    symbol: "TT1",
    price: 0.00052153,
    change: -5.2,
    image: "/placeholder.svg?height=40&width=40",
  },
  {
    id: "3",
    name: "Top Token 01",
    symbol: "TT1",
    price: 0.00052153,
    change: 8.7,
    image: "/placeholder.svg?height=40&width=40",
  },
]

const featuredTokens = [
  {
    id: "1",
    name: "Greed3",
    symbol: "Greed3",
    marketCap: "$71,867",
    created: "02/20/2023",
    bonded: "02/20/2023",
    fiveMin: "-0.31",
    oneHour: "-1.78",
    sixHour: "-0.31",
    twentyFourHour: "0.3",
    sevenDay: "39.27",
    chart: "up",
  },
  {
    id: "2",
    name: "Greed3",
    symbol: "Greed3",
    marketCap: "$71,867",
    created: "02/20/2023",
    bonded: "02/20/2023",
    fiveMin: "-0.31",
    oneHour: "-1.78",
    sixHour: "-0.31",
    twentyFourHour: "0.3",
    sevenDay: "39.27",
    chart: "down",
  },
  {
    id: "3",
    name: "Greed3",
    symbol: "Greed3",
    marketCap: "$71,867",
    created: "02/20/2023",
    bonded: "02/20/2023",
    fiveMin: "-0.31",
    oneHour: "-1.78",
    sixHour: "-0.31",
    twentyFourHour: "0.3",
    sevenDay: "39.27",
    chart: "down",
  },
]

const transactions = [
  {
    id: "1",
    date: "02/21/2023, 01:40:52 AM",
    owner: "Alex 001",
    type: "Sale",
    tradedToken: "01",
    usd: "753,099,856",
    tokenAmount: "-85.01",
    hash: "100",
  },
  {
    id: "2",
    date: "02/21/2023, 01:40:52 AM",
    owner: "Alex 001",
    type: "Sale",
    tradedToken: "01",
    usd: "753,099,856",
    tokenAmount: "-85.01",
    hash: "100",
  },
  {
    id: "3",
    date: "02/21/2023, 01:40:52 AM",
    owner: "Alex 001",
    type: "Sale",
    tradedToken: "01",
    usd: "753,099,856",
    tokenAmount: "-85.01",
    hash: "100",
  },
  {
    id: "4",
    date: "02/21/2023, 01:40:52 AM",
    owner: "Alex 001",
    type: "Sale",
    tradedToken: "01",
    usd: "753,099,856",
    tokenAmount: "-85.01",
    hash: "100",
  },
  {
    id: "5",
    date: "02/21/2023, 01:40:52 AM",
    owner: "Alex 001",
    type: "Sale",
    tradedToken: "01",
    usd: "753,099,856",
    tokenAmount: "-85.01",
    hash: "100",
  },
]

export default function Dashboard() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Welcome to Rader Dashboard!</CardTitle>
        </CardHeader>
        <CardContent>
          <p>🎉 Authentication is working! You are now logged in and can access the dashboard.</p>
          <p className="mt-4 text-sm text-gray-600">
            This is a simplified dashboard page. The full dashboard with all components will be restored once we confirm the authentication is working properly.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
