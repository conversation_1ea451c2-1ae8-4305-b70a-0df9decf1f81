// Test script to verify authentication flow
const baseUrl = 'http://localhost:3001';

async function testAuthFlow() {
  console.log('Testing authentication flow...\n');

  // Test 1: Signup
  console.log('1. Testing signup...');
  try {
    const signupResponse = await fetch(`${baseUrl}/api/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Test User 2',
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    const signupData = await signupResponse.json();
    console.log('Signup response:', signupResponse.status, signupData);

    if (signupResponse.ok) {
      console.log('✅ Signup successful');
      
      // Test 2: Login with the same credentials
      console.log('\n2. Testing login...');
      const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      });

      const loginData = await loginResponse.json();
      console.log('Login response:', loginResponse.status, loginData);

      if (loginResponse.ok) {
        console.log('✅ Login successful');
        
        // Test 3: Access protected route with token
        console.log('\n3. Testing protected route access...');
        const meResponse = await fetch(`${baseUrl}/api/auth/me`, {
          headers: {
            'Authorization': `Bearer ${loginData.token}`
          }
        });

        const meData = await meResponse.json();
        console.log('Protected route response:', meResponse.status, meData);

        if (meResponse.ok) {
          console.log('✅ Protected route access successful');
        } else {
          console.log('❌ Protected route access failed');
        }
      } else {
        console.log('❌ Login failed');
      }
    } else {
      console.log('❌ Signup failed');
    }
  } catch (error) {
    console.error('Error during testing:', error);
  }
}

// Run the test if this is run directly with Node.js
if (typeof window === 'undefined') {
  testAuthFlow();
}
