"use client";

import { useAuth } from "@/lib/auth-context";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

export default function Dashboard() {
  const { user, logout } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <Button onClick={logout} variant="outline">
            Logout
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Welcome to Rader!</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg mb-4">
              🎉 Authentication is working perfectly!
            </p>
            <div className="space-y-2">
              <p><strong>Name:</strong> {user?.name}</p>
              <p><strong>Email:</strong> {user?.email}</p>
              <p><strong>Subscription Status:</strong> {user?.isSubscribed ? 'Active' : 'Free'}</p>
            </div>
            <p className="mt-6 text-sm text-gray-600">
              Your login/signup functionality is now working correctly. You can now build your token tracking features on top of this authentication system.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
