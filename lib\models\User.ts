import mongoose, { Document, Schema } from 'mongoose';

export interface IUser extends Document {
  name: string;
  email: string;
  password: string;
  isSubscribed: boolean;
  subscriptionExpiry?: Date;
  subscriptionType?: 'monthly' | 'yearly';
  createdAt: Date;
  updatedAt: Date;
}

const UserSchema: Schema = new Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    minlength: [2, 'Name must be at least 2 characters long'],
    maxlength: [50, 'Name cannot exceed 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [
      /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/,
      'Please enter a valid email address'
    ]
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [6, 'Password must be at least 6 characters long']
  },
  isSubscribed: {
    type: Boolean,
    default: false
  },
  subscriptionExpiry: {
    type: Date,
    default: null
  },
  subscriptionType: {
    type: String,
    enum: ['monthly', 'yearly'],
    default: null
  }
}, {
  timestamps: true
});

// Index for email for faster queries
UserSchema.index({ email: 1 });

// Index for subscription expiry for cleanup tasks
UserSchema.index({ subscriptionExpiry: 1 });

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
